import { CheckBookAvailabilityInterface } from '@/redux/interfaces/properties';
import { BookingAvailabilityData } from '@/types/booking';
import { Nullable } from '@/types/common';

import { addDays, differenceInCalendarDays } from 'date-fns';
import { add, addMonths } from 'date-fns';

import { currencyFormatter } from './common';

export const getBookingUpdatedValues = (
  checkBookAvailability: CheckBookAvailabilityInterface,
  petQuestion: string,
) => {
  const rent = Number(checkBookAvailability?.rent ?? 0);
  const pet_fee = petQuestion === 'yes' ? Number(checkBookAvailability?.pet_fee ?? 0) : 0;
  const otherFees = Number(checkBookAvailability?.other_fees ?? 0);
  const nantucketRentalsFee = Number(checkBookAvailability?.nantucket_fee ?? 0); // Nantucket Rentals Fee = 10% of Rent
  // if charge_community_impact_fee is true then additional 3%(ie. 14.7%) tax will be applied along with existing 11.7%
  const occupancyTaxPercentage = checkBookAvailability?.charge_community_impact_fee ? 0.147 : 0.117;
  const occupancyTax =
    checkBookAvailability?.occupancy_tax === 0
      ? 0
      : (rent + nantucketRentalsFee + otherFees + pet_fee) * occupancyTaxPercentage; // Occupancy Tax = 11.7% of (Rent + Nantucket Rentals Fee + Other Fees)
  const totalWithoutTaxes = rent + nantucketRentalsFee + otherFees + pet_fee;
  const grandTotal = totalWithoutTaxes + occupancyTax;
  return { occupancyTax, totalWithoutTaxes, grandTotal };
};

export const getBookingCalculatedValues = (
  availabilityData: Nullable<BookingAvailabilityData>,
  isPetSelected: boolean,
) => {
  const rent = Number(availabilityData?.rent ?? 0);
  const pet_fee = isPetSelected ? Number(availabilityData?.pet_fee ?? 0) : 0;
  const otherFees = Number(availabilityData?.other_fees ?? 0);
  const nantucketRentalsFee = Number(availabilityData?.nantucket_fee ?? 0); // Nantucket Rentals Fee = 10% of Rent
  // if charge_community_impact_fee is true then additional 3%(ie. 14.7%) tax will be applied along with existing 11.7%
  const occupancyTaxPercentage = availabilityData?.charge_community_impact_fee ? 0.147 : 0.117;
  const occupancyTax =
    availabilityData?.occupancy_tax === 0
      ? 0
      : (rent + nantucketRentalsFee + otherFees + pet_fee) * occupancyTaxPercentage; // Occupancy Tax = 11.7% of (Rent + Nantucket Rentals Fee + Other Fees)
  const totalWithoutTaxes = rent + nantucketRentalsFee + otherFees + pet_fee;
  const grandTotal = totalWithoutTaxes + occupancyTax;
  return { occupancyTax, totalWithoutTaxes, grandTotal };
};

export const getUpfrontPaymenetAmount = (
  checkBookAvailability: CheckBookAvailabilityInterface,
  petsIncluded: boolean,
) => {
  const numberOfPayments = checkBookAvailability.payment_schedule?.length ?? 1;
  const rent = Number(checkBookAvailability?.rent ?? 0);
  const otherFees = Number(checkBookAvailability?.other_fees ?? 0);
  const nantucketRentalsFee = Number(checkBookAvailability?.nantucket_fee ?? 0);
  const pet_fee = petsIncluded ? Number(checkBookAvailability?.pet_fee ?? 0) : 0;
  const occupancyTax =
    checkBookAvailability?.occupancy_tax === 0
      ? 0
      : (rent + nantucketRentalsFee + otherFees + pet_fee) * 0.117; // Occupancy Tax = 11.7% of (Rent + Nantucket Rentals Fee + Other Fees)
  const baseRent = rent / numberOfPayments;
  return baseRent + occupancyTax + nantucketRentalsFee + otherFees + pet_fee;
};

export const getDiscountName = (discountName: string) => {
  if (discountName === 'Custom Discount') {
    return 'Discount Applied';
  } else {
    return discountName;
  }
};

export const getRentDayWiseBreakupString = (
  from: Date,
  to: Date,
  rent: number,
  disount: number,
) => {
  const daysInBetween = differenceInCalendarDays(to, from);
  return `${currencyFormatter.format(
    Number((rent + disount) / daysInBetween),
  )} x ${daysInBetween} nights`;
};
