'use server';

import { AuthData, isValidJsonString, NR_ACCESS_COOKIE } from '@/utils/api-utils/cookie';

import { cookies } from 'next/headers';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const checkBookingAvailability = async <T>(
  propertyId: number,
  adultsCount: number,
  childrenCount: number,
  from: string,
  to: string,
  bookingId?: number,
) => {
  const cookieStore = await cookies();
  const decryptedAuthString = Buffer.from(
    cookieStore.get(NR_ACCESS_COOKIE)?.value || '',
    'base64',
  ).toString();
  const decryptedAuthData: AuthData =
    isValidJsonString(decryptedAuthString) && JSON.parse(decryptedAuthString);

  const fetchURL = bookingId
    ? `/property/nrproperty-listing/check-booking-availability?nrPropertyId=${propertyId}&adultsCount=${adultsCount}&childrenCount=${childrenCount}&dateFrom=${from}&dateTo=${to}&bookingId=${bookingId}`
    : `/property/nrproperty-listing/check-booking-availability?nrPropertyId=${propertyId}&adultsCount=${adultsCount}&childrenCount=${childrenCount}&dateFrom=${from}&dateTo=${to}`;

  try {
    const res = await fetch(`${BASE_URL}${fetchURL}`, {
      headers: {
        Authorization: `Bearer ${decryptedAuthData.access_token}`,
      },
      cache: 'no-store',
      next: { tags: [fetchURL] },
    });

    return res.json() as T;
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to fetch listing booking availability data');
  }
};
