'use client';

import { memo, useCallback, useState } from 'react';

import Checkbox from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { useBookingForm } from '@/contexts/BookingFormContext';
import { useRequestToBook } from '@/contexts/RequestToBookContext';
import { currencyFormatter } from '@/utils/common';

import classNames from 'classnames';
import dynamic from 'next/dynamic';

const TravelInsuranceDialog = dynamic(() => import('./TravelInsuranceDialog'), {
  ssr: false,
});

const BookingTravelInsurance = memo(({ hiddenMd }: { hiddenMd?: boolean }) => {
  const [showDialog, setShowDialog] = useState<boolean>(false);
  const { bookingAvailabilityData } = useRequestToBook();
  const { isInsuranceAdded, setIsInsuranceAdded } = useBookingForm();

  const onToggle = useCallback(() => {
    setIsInsuranceAdded(!isInsuranceAdded);
  }, [isInsuranceAdded, setIsInsuranceAdded]);

  const onToggleDialog = useCallback(() => {
    setShowDialog((_s) => !_s);
  }, []);
  return (
    <>
      <div
        className={classNames(
          'trip-travel-insurance order-6 w-full md:w-[500px] md:bg-grey-light md:p-3 border md:border-grey-border rounded-[10px]',
          {
            'md:hidden': hiddenMd,
          },
        )}
      >
        <Separator className="-ml-4 w-[calc(100%+32px)] my-4 h-1 md:hidden" />
        <p className="m-0 text-sm font-semibold">
          Add travel protection? <br className="md:hidden" />
          <a
            target="_blank"
            href="https://www.generalitravelinsurance.com/customer/lookup.html?planName=GR330"
            className="text-inherit underline text-xs"
          >
            View complete Plan Details
          </a>
        </p>
        <div className="bg-grey-light md:bg-transparent p-2 md:p-0 mt-2 md:mt-0 rounded-[10px]">
          <div className="flex items-center space-x-2 md:my-2">
            <Checkbox id="travelInsurance" onChange={onToggle} checked={isInsuranceAdded} />
            <label htmlFor="ach" className="cursor-pointer text-xs md:text-sm">
              Yes, add Travel Protection Plan for{' '}
              {currencyFormatter.format(bookingAvailabilityData?.travel_insurance_amount ?? 0)}
            </label>
          </div>
          <p className="m-0 text-[10px]">
            The plan cost includes the travel insurance premium and assistance services fee. Travel
            insurance coverages are underwritten by: Generali U.S. Branch, New York, NY; NAIC #
            11231, for the operating name used in certain states, and other important information
            about the Travel Insurance & Assistance Services Plan, please see{' '}
            <a
              href="https://www.generalitravelinsurance.com/customer/disclosures.html"
              target="_blank"
            >
              Important Disclosures.
            </a>
          </p>
          <p
            className="underline text-[10px] md:text-xs cursor-pointer my-0 md:my-2"
            onClick={onToggleDialog}
          >
            What&apos;s covered?
          </p>
        </div>
      </div>
      {showDialog && (
        <TravelInsuranceDialog
          addedInsurance={isInsuranceAdded}
          setAddedInsurance={setIsInsuranceAdded}
          insuranceAmount={bookingAvailabilityData?.travel_insurance_amount ?? 0}
          open
          handleClose={() => setShowDialog(false)}
        />
      )}
    </>
  );
});

export default BookingTravelInsurance;
