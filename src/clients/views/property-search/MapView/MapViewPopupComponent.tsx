'use client';

import { memo } from 'react';

import { currencyFormatterRound, numberWithCommas } from '@/utils/common';

import classNames from 'classnames';
import Image from 'next/image';
import Link from 'next/link';

type Props = {
  popupInfo: any;
  setPopupInfo: (val: any) => void;
};

const PopupComponent = ({ popupInfo }: Props) => {
  return (
    <Link
      href={popupInfo.slug}
      className="w-full h-full overflow-hidden rounded-xl no-underline outline-none"
    >
      <Image
        alt="Listing image"
        id="ImagePopup"
        width={280}
        height={160}
        className="object-cover w-full h-[160px]"
        src={popupInfo.NrPropertyPics[0]?.thumbnail}
        quality={60}
      />
      <div className="p-2.5 w-full bg-white">
        <div className="text-sm md:text-base font-medium text-primary-blue text-left">
          Up to {currencyFormatterRound.format(Number(popupInfo?.NrRentalRates?.[0]?.rate ?? 0))}
          /night
        </div>
        <hr className="mb-4 md:mb-6 border border-solid border-transparent border-t-grey-border w-full" />
        <div className="flex items-center justify-between w-full text-[#6D83A3]">
          <div
            className={classNames('flex items-center justify-between gap-x-1', {
              'gap-x-2': popupInfo?.totalLivingArea === 0,
            })}
          >
            <div className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="17"
                height="16"
                viewBox="0 0 17 16"
                fill="none"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M7.62908 2.16663H9.03763C10.2628 2.16662 11.2332 2.16661 11.9927 2.26872C12.7743 2.3738 13.4069 2.59521 13.9059 3.09412C14.4048 3.59303 14.6262 4.22566 14.7313 5.00728C14.8322 5.75779 14.8333 6.7143 14.8334 7.91908C15.0626 8.10823 15.2446 8.35208 15.3605 8.63171C15.4407 8.8253 15.4717 9.02498 15.4862 9.23638C15.5 9.43946 15.5 9.68682 15.5 9.98312V10.0168C15.5 10.3131 15.5 10.5605 15.4862 10.7635C15.4717 10.9749 15.4407 11.1746 15.3605 11.3682C15.1744 11.8174 14.8175 12.1743 14.3683 12.3604C14.1747 12.4406 13.975 12.4717 13.7636 12.4861C13.6828 12.4916 13.5951 12.4949 13.5 12.4969V13.3333C13.5 13.6094 13.2762 13.8333 13 13.8333C12.7239 13.8333 12.5 13.6094 12.5 13.3333V12.5H4.16669V13.3333C4.16669 13.6094 3.94283 13.8333 3.66669 13.8333C3.39054 13.8333 3.16669 13.6094 3.16669 13.3333V12.4969C3.07163 12.4949 2.98387 12.4916 2.90311 12.4861C2.69171 12.4717 2.49203 12.4406 2.29843 12.3604C1.84922 12.1743 1.49231 11.8174 1.30624 11.3682C1.22605 11.1746 1.19496 10.9749 1.18053 10.7635C1.16668 10.5605 1.16668 10.3131 1.16669 10.0168V9.98313C1.16668 9.68682 1.16668 9.43946 1.18053 9.23638C1.19496 9.02498 1.22605 8.8253 1.30624 8.63171C1.42207 8.35208 1.60408 8.10823 1.83335 7.91908C1.83336 6.7143 1.83454 5.75779 1.93544 5.00728C2.04053 4.22566 2.26194 3.59303 2.76085 3.09412C3.25976 2.59521 3.89239 2.3738 4.67401 2.26872C5.43348 2.16661 6.4039 2.16662 7.62908 2.16663ZM2.83361 7.51923C2.85664 7.51719 2.8798 7.5154 2.90311 7.51381C3.07095 7.50235 3.26904 7.50037 3.50002 7.50003L3.50002 6.96529C3.5 6.36631 3.49998 5.86683 3.55329 5.47031C3.60956 5.05178 3.73334 4.67391 4.03699 4.37026C4.34064 4.06662 4.7185 3.94284 5.13703 3.88657C5.53355 3.83326 6.03303 3.83327 6.63202 3.83329H10.0347C10.6337 3.83327 11.1332 3.83326 11.5297 3.88657C11.9482 3.94284 12.3261 4.06662 12.6297 4.37026C12.9334 4.67391 13.0571 5.05178 13.1134 5.47031C13.1667 5.86683 13.1667 6.36631 13.1667 6.9653V7.50003C13.3977 7.50037 13.5958 7.50235 13.7636 7.51381C13.7869 7.5154 13.8101 7.51719 13.8331 7.51923C13.8315 6.49599 13.8202 5.73593 13.7402 5.14053C13.65 4.4698 13.4809 4.08337 13.1988 3.80123C12.9166 3.51909 12.5302 3.34998 11.8595 3.2598C11.1743 3.16769 10.2712 3.16663 9.00002 3.16663H7.66669C6.39547 3.16663 5.49236 3.16769 4.80725 3.2598C4.13653 3.34998 3.7501 3.51909 3.46796 3.80123C3.18582 4.08337 3.0167 4.4698 2.92653 5.14053C2.84648 5.73593 2.83519 6.49599 2.83361 7.51923ZM12.1667 7.49996V6.99996C12.1667 6.35729 12.1656 5.92558 12.1223 5.60355C12.081 5.29591 12.0095 5.16425 11.9226 5.07737C11.8357 4.99049 11.7041 4.91901 11.3964 4.87765C11.0744 4.83436 10.6427 4.83329 10 4.83329H8.83335V7.49996H12.1667ZM7.83335 7.49996V4.83329H6.66669C6.02401 4.83329 5.59231 4.83436 5.27028 4.87765C4.96264 4.91901 4.83098 4.99049 4.7441 5.07737C4.65722 5.16425 4.58574 5.29591 4.54438 5.60355C4.50108 5.92558 4.50002 6.35729 4.50002 6.99996V7.49996H7.83335ZM2.97118 8.51149C2.8107 8.52244 2.73255 8.54209 2.68112 8.56339C2.47693 8.64797 2.3147 8.8102 2.23012 9.01439C2.20882 9.06583 2.18916 9.14397 2.17821 9.30445C2.16696 9.46943 2.16669 9.68249 2.16669 9.99996C2.16669 10.3174 2.16696 10.5305 2.17821 10.6955C2.18916 10.856 2.20882 10.9341 2.23012 10.9855C2.3147 11.1897 2.47693 11.3519 2.68112 11.4365C2.73255 11.4578 2.8107 11.4775 2.97118 11.4884C3.13616 11.4997 3.34922 11.5 3.66669 11.5H13C13.3175 11.5 13.5306 11.4997 13.6955 11.4884C13.856 11.4775 13.9342 11.4578 13.9856 11.4365C14.1898 11.3519 14.352 11.1897 14.4366 10.9855C14.4579 10.9341 14.4775 10.856 14.4885 10.6955C14.4997 10.5305 14.5 10.3174 14.5 9.99996C14.5 9.68249 14.4997 9.46943 14.4885 9.30445C14.4775 9.14397 14.4579 9.06583 14.4366 9.01439C14.352 8.8102 14.1898 8.64797 13.9856 8.56339C13.9342 8.54209 13.856 8.52244 13.6955 8.51149C13.5306 8.50023 13.3175 8.49996 13 8.49996H3.66669C3.34922 8.49996 3.13616 8.50023 2.97118 8.51149Z"
                  fill="#6D83A3"
                />
              </svg>
              <span className="ml-2">{popupInfo?.totalBedrooms}</span>
            </div>
            <div className="w-[1px] h-[20px] bg-grey-border mx-4" />
            <div className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="17"
                height="16"
                viewBox="0 0 17 16"
                fill="none"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M4.09015 1.83337C3.4881 1.83337 3.00004 2.32143 3.00004 2.92348V7.50004H3.18956C3.19681 7.50003 3.20403 7.50003 3.21121 7.50004C3.21476 7.50004 3.2183 7.50004 3.22183 7.50004L13.7889 7.50004C13.796 7.50003 13.8033 7.50003 13.8105 7.50004H15.1667C15.4428 7.50004 15.6667 7.7239 15.6667 8.00004C15.6667 8.27618 15.4428 8.50004 15.1667 8.50004H14.9881C14.9924 8.53 14.9956 8.56034 14.9976 8.591C15.0001 8.62949 15.0001 8.67111 15 8.71121L15 8.73648C15 8.98662 15 9.13327 14.9897 9.29681C14.895 10.7877 14.0894 12.1645 12.9218 13.0647C12.9309 13.0791 12.9394 13.0942 12.9473 13.1098L13.6139 14.4431C13.7374 14.6901 13.6373 14.9904 13.3903 15.1139C13.1433 15.2374 12.843 15.1373 12.7195 14.8903L12.0744 13.6001C11.5399 13.8706 10.9586 14.0487 10.354 14.1099C10.191 14.1264 10.0915 14.1302 9.92205 14.1365L9.91759 14.1367C9.4241 14.1552 8.94104 14.1667 8.50004 14.1667C8.05904 14.1667 7.57598 14.1552 7.08249 14.1367L7.07804 14.1365C6.90856 14.1302 6.80909 14.1264 6.64611 14.1099C6.04152 14.0487 5.46023 13.8706 4.92571 13.6001L4.28059 14.8903C4.15709 15.1373 3.85676 15.2374 3.60977 15.1139C3.36278 14.9904 3.26267 14.6901 3.38616 14.4431L4.05283 13.1098C4.06063 13.0942 4.06915 13.0791 4.07831 13.0647C2.91068 12.1645 2.10504 10.7877 2.01042 9.29681C2.00003 9.13325 2.00004 8.9866 2.00004 8.73641L2.00004 8.72182C2.00004 8.7183 2.00004 8.71476 2.00004 8.71121C2.00003 8.69889 2.00002 8.68642 2.00009 8.67394C2.00006 8.67153 2.00004 8.66912 2.00004 8.66671V8.50004H1.83337C1.55723 8.50004 1.33337 8.27618 1.33337 8.00004C1.33337 7.7239 1.55723 7.50004 1.83337 7.50004H2.00004V2.92348C2.00004 1.76915 2.93581 0.833374 4.09015 0.833374C4.94481 0.833374 5.71336 1.35371 6.03077 2.14724L6.10419 2.3308C6.63306 2.19663 7.20693 2.22772 7.74781 2.4588C8.42469 2.74797 8.91722 3.29357 9.16946 3.93373C9.26847 4.185 9.15004 4.46949 8.90198 4.57628L4.92814 6.287C4.80386 6.3405 4.66319 6.34135 4.53828 6.28935C4.41336 6.23735 4.31485 6.13694 4.26524 6.01105C4.0131 5.37114 3.99799 4.63014 4.27819 3.94013C4.47766 3.44894 4.80147 3.04623 5.19781 2.75742L5.10229 2.51863C4.93674 2.10476 4.5359 1.83337 4.09015 1.83337ZM3.22183 8.50004C3.19512 8.50004 3.17922 8.50005 3.16721 8.50018C3.15847 8.50028 3.15536 8.50042 3.15525 8.50043C3.15481 8.50046 3.15521 8.50043 3.15525 8.50043C3.07187 8.50585 3.00606 8.57212 3.00042 8.65544C3.00039 8.65623 3.00027 8.65957 3.00018 8.66721C3.00005 8.67922 3.00004 8.69512 3.00004 8.72182C3.00004 8.99026 3.00025 9.1049 3.00841 9.23347C3.13203 11.1812 4.80512 12.9184 6.74687 13.115C6.87684 13.1282 6.94878 13.131 7.12003 13.1374C7.60539 13.1556 8.07528 13.1667 8.50004 13.1667C8.9248 13.1667 9.39469 13.1556 9.88005 13.1374C10.0513 13.131 10.1232 13.1282 10.2532 13.115C12.195 12.9184 13.8681 11.1812 13.9917 9.23347C13.9998 9.1049 14 8.99026 14 8.72183C14 8.69512 14 8.67922 13.9999 8.66721C13.9998 8.66045 13.9997 8.65706 13.9997 8.65582C13.9996 8.65505 13.9996 8.65497 13.9997 8.65582C13.9942 8.57229 13.9277 8.50582 13.8442 8.50039C13.845 8.50044 13.8452 8.50043 13.8442 8.50039C13.8428 8.50034 13.8394 8.50026 13.8329 8.50018C13.8209 8.50005 13.805 8.50004 13.7783 8.50004H3.22183ZM7.35495 3.37839C6.91675 3.19119 6.44306 3.21723 6.03813 3.41436C5.67889 3.58924 5.37416 3.89911 5.20471 4.31638C5.0957 4.58481 5.05628 4.86441 5.07769 5.13389L7.99846 3.87651C7.8322 3.6623 7.61426 3.48918 7.35495 3.37839Z"
                  fill="#6D83A3"
                />
              </svg>
              <span className="ml-2">{popupInfo?.totalBathrooms}</span>
            </div>
            <div className="w-[1px] h-[20px] bg-grey-border mx-4" />
            <div className="flex items-center">
              {popupInfo?.totalCapacity}
              <span className="ml-2">Guests</span>
            </div>
          </div>
          {popupInfo?.totalLivingArea > 0 && (
            <div className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
              >
                <g clipPath="url(#clip0_22699_6241)">
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M3.96527 0.833375L5.9994 0.833376C5.99923 0.833376 5.99958 0.833375 5.9994 0.833376C5.99958 0.833376 6.00029 0.833375 6.00047 0.833376H7.99948C7.99933 0.833376 7.99963 0.833375 7.99948 0.833376C7.99963 0.833376 8.00024 0.833375 8.00039 0.833376H9.99959C9.99947 0.833376 9.9997 0.833375 9.99959 0.833376C9.9997 0.833376 10.0002 0.833375 10.0003 0.833376L12.3588 0.833375C12.8052 0.833364 13.1777 0.833355 13.4793 0.864031C13.7949 0.896141 14.0878 0.96595 14.3518 1.14235C14.552 1.2761 14.7239 1.44798 14.8576 1.64816C15.034 1.91216 15.1038 2.20507 15.1359 2.52072C15.1666 2.82228 15.1666 3.19477 15.1666 3.64116V3.69225C15.1666 4.13865 15.1666 4.51114 15.1359 4.8127C15.1038 5.12834 15.034 5.42126 14.8576 5.68525C14.7239 5.88544 14.552 6.05731 14.3518 6.19107C14.0878 6.36747 13.7949 6.43728 13.4793 6.46939C13.1777 6.50006 12.8052 6.50005 12.3588 6.50004L7.33327 6.50004C7.00486 6.50004 6.80886 6.5011 6.66838 6.51999C6.60401 6.52865 6.57177 6.53884 6.55725 6.54481C6.55388 6.5462 6.55167 6.54728 6.55038 6.54795L6.54876 6.54887L6.54784 6.55049C6.54717 6.55178 6.54609 6.55399 6.54471 6.55735C6.53873 6.57187 6.52854 6.60411 6.51989 6.66848C6.501 6.80897 6.49994 7.00497 6.49994 7.33337L6.49994 12.3589C6.49995 12.8053 6.49996 13.1778 6.46928 13.4794C6.43717 13.795 6.36736 14.0879 6.19096 14.3519C6.05721 14.5521 5.88533 14.724 5.68515 14.8577C5.42115 15.0341 5.12824 15.1039 4.81259 15.1361C4.51103 15.1667 4.13854 15.1667 3.69215 15.1667H3.64106C3.19466 15.1667 2.82217 15.1667 2.52062 15.1361C2.20497 15.1039 1.91205 15.0341 1.64806 14.8577C1.44787 14.724 1.276 14.5521 1.14224 14.3519C0.965844 14.0879 0.896034 13.795 0.863924 13.4794C0.833248 13.1778 0.833257 12.8053 0.833268 12.3589L0.833268 3.96538C0.833248 3.36639 0.833232 2.86691 0.886543 2.47039C0.942813 2.05186 1.06659 1.67399 1.37024 1.37035C1.67389 1.0667 2.05175 0.942919 2.47028 0.88665C2.8668 0.833339 3.36628 0.833355 3.96527 0.833375ZM1.83327 6.50004H3.33327C3.60941 6.50004 3.83327 6.27618 3.83327 6.00004C3.83327 5.7239 3.60941 5.50004 3.33327 5.50004H1.83327V4.00004C1.83327 3.35737 1.83433 2.92566 1.87763 2.60364C1.91899 2.296 1.99047 2.16433 2.07735 2.07745C2.16422 1.99058 2.29589 1.91909 2.60353 1.87773C2.92555 1.83444 3.35726 1.83338 3.99994 1.83338H5.49994L5.49994 3.33338C5.49994 3.60952 5.72379 3.83337 5.99994 3.83338C6.27608 3.83338 6.49994 3.60952 6.49994 3.33338L6.49994 1.83338H7.49994V2.66671C7.49994 2.94285 7.72379 3.16671 7.99994 3.16671C8.27608 3.16671 8.49994 2.94285 8.49994 2.66671V1.83338H9.49994V3.33338C9.49994 3.60952 9.72379 3.83338 9.99994 3.83338C10.2761 3.83338 10.4999 3.60952 10.4999 3.33338V1.83338H11.4999V2.66671C11.4999 2.94285 11.7238 3.16671 11.9999 3.16671C12.2761 3.16671 12.4999 2.94285 12.4999 2.66671V1.83344C12.8883 1.83388 13.1627 1.83699 13.3781 1.8589C13.6146 1.88296 13.7239 1.92551 13.7962 1.97382C13.8872 2.03462 13.9654 2.11274 14.0262 2.20373C14.0745 2.27603 14.117 2.38535 14.1411 2.62193C14.166 2.86686 14.1666 3.18815 14.1666 3.66671C14.1666 4.14527 14.166 4.46655 14.1411 4.71149C14.117 4.94807 14.0745 5.05738 14.0262 5.12968C13.9654 5.22067 13.8872 5.2988 13.7962 5.3596C13.7239 5.40791 13.6146 5.45045 13.3781 5.47452C13.1331 5.49944 12.8118 5.50004 12.3333 5.50004H7.33327C7.3233 5.50004 7.31335 5.50004 7.30344 5.50004C7.01489 5.50001 6.75034 5.49997 6.53513 5.52891C6.29814 5.56077 6.04766 5.63574 5.84164 5.84175C5.63563 6.04777 5.56066 6.29825 5.5288 6.53524C5.49987 6.75045 5.4999 7.015 5.49993 7.30355C5.49993 7.31346 5.49994 7.3234 5.49994 7.33337V12.3334C5.49994 12.8119 5.49933 13.1332 5.47441 13.3782C5.45035 13.6147 5.4078 13.724 5.35949 13.7964C5.29869 13.8873 5.22057 13.9655 5.12958 14.0263C5.05728 14.0746 4.94796 14.1171 4.71138 14.1412C4.46645 14.1661 4.14516 14.1667 3.6666 14.1667C3.18804 14.1667 2.86676 14.1661 2.62182 14.1412C2.38524 14.1171 2.27593 14.0746 2.20363 14.0263C2.11264 13.9655 2.03451 13.8873 1.97371 13.7964C1.9254 13.724 1.88286 13.6147 1.85879 13.3782C1.83688 13.1628 1.83377 12.8884 1.83334 12.5H2.6666C2.94274 12.5 3.1666 12.2762 3.1666 12C3.1666 11.7239 2.94274 11.5 2.6666 11.5H1.83327V10.5L3.33327 10.5C3.60941 10.5 3.83327 10.2762 3.83327 10C3.83327 9.7239 3.60941 9.50004 3.33327 9.50004L1.83327 9.50004V8.50004H2.6666C2.94274 8.50004 3.1666 8.27618 3.1666 8.00004C3.1666 7.7239 2.94274 7.50004 2.6666 7.50004H1.83327V6.50004Z"
                    fill="#6D83A3"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_22699_6241">
                    <rect x="-6.10352e-05" width="16" height="16" rx="5" fill="white" />
                  </clipPath>
                </defs>
              </svg>
              <span className="ml-2">{numberWithCommas(popupInfo?.totalLivingArea)} SF</span>
            </div>
          )}
        </div>
      </div>
    </Link>
  );
};

export default memo(PopupComponent);
