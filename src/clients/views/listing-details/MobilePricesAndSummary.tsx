'use client';

import { useCallback, useState } from 'react';

import { XCircleIcon } from '@heroicons/react/24/outline';

import Button from '@/clients/ui/Button';
import { useRequestToBook } from '@/contexts/RequestToBookContext';
import { BookingAvailabilityData, FormattedBookingData } from '@/types/booking';
import { Nullable } from '@/types/common';
import { PropertyDetails } from '@/types/properties';
import { getStringSingularPlural } from '@/utils/common';

import { format } from 'date-fns';
import { Drawer } from 'vaul';

import MobileSelectGuest from './MobileSelectGuest';
import PricesAndSummary from './PricesAndSummary';

type Props = {
  onToggle: () => void;
  onToggleDatePicker: () => void;
  bookingData: Nullable<FormattedBookingData>;
  petsAllowed?: boolean;
  property: PropertyDetails;
  capacity?: number;
  numberOfNights: number;
};

const MobilePricesAndSummary = ({
  onToggle,
  onToggleDatePicker,
  bookingData,
  property,
  petsAllowed,
  capacity,
  numberOfNights,
}: Props) => {
  const { guests, date, petCount, isPetSelected } = useRequestToBook();
  const [openPets, setOpenPets] = useState<boolean>(false);

  const onTogglePetsDialog = useCallback(() => {
    setOpenPets(!openPets);
  }, [openPets]);
  return (
    <>
      <div className="p-4 bg-white rounded-t-[10px] border border-solid border-[#E5E5E5]">
        <div className="flex items-start justify-between">
          <p className="m-0 text-xl font-medium">Price Details</p>
          <Button onClick={onToggle} intent="ghost" className="!p-0">
            <XCircleIcon className="w-6 h-6" />
          </Button>
        </div>
        <PricesAndSummary bookingData={bookingData} numberOfNights={numberOfNights} />
        <p className="m-0 mb-1 pl-3.5 uppercase text-xs text-metal-gray">Date</p>
        <div className="border border-solid border-platinium rounded-[40px] px-3.5 py-2.5 flex items-center justify-between">
          <span>
            {date?.from && format(date.from, 'MMM d')}-{date?.to && format(date.to, 'd')}
          </span>
          <Button
            onClick={onToggleDatePicker}
            intent="ghost"
            className="font-normal text-sm text-foundation-blue !p-0"
          >
            Change
          </Button>
        </div>
        <p className="m-0 my-1 pl-3.5 uppercase text-xs text-metal-gray">Guests</p>
        <div className="border border-solid border-platinium rounded-[40px] px-3.5 py-2.5 flex items-center justify-between">
          <span>
            {getStringSingularPlural('Adult', 'Adults', guests.adults)},{' '}
            {getStringSingularPlural('Child', 'Children', guests.children)}
            {isPetSelected &&
              petCount > 0 &&
              `, ${getStringSingularPlural('Pet', 'Pets', petCount)}`}
          </span>
          <Button
            onClick={onTogglePetsDialog}
            intent="ghost"
            className="font-normal text-sm text-foundation-blue !p-0"
          >
            Change
          </Button>
        </div>
        <p className="m-0 my-2 uppercase text-xs text-metal-gray">
          This property has a maximum of {capacity} guests.{' '}
          {!petsAllowed
            ? `Pets are not allowed.`
            : `Pets allowed with prior permission, fees may apply.`}
        </p>
        <Button className="w-full rounded-[32px]" onClick={onToggle}>
          Continue
        </Button>
      </div>
      <Drawer.Root direction="bottom" open={openPets} onOpenChange={onTogglePetsDialog}>
        <Drawer.Portal>
          <Drawer.Overlay className="fixed inset-0 bg-black/60 z-50" />
          <Drawer.Content className="bg-gray-100 flex flex-col rounded-t-[10px] mt-24 fixed bottom-0 left-0 right-0 outline-none z-50">
            <Drawer.Title className="hidden"></Drawer.Title>
            <Drawer.Description className="hidden"></Drawer.Description>
            {openPets && (
              <MobileSelectGuest
                property={property}
                onToggle={onTogglePetsDialog}
                petsAllowed={petsAllowed}
                capacity={capacity}
              />
            )}
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>
    </>
  );
};

export default MobilePricesAndSummary;
