'use client';

import throttle from 'lodash/throttle';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { DateRange, DayContentProps, SelectRangeEventHandler } from 'react-day-picker';

import Button from '@/clients/ui/Button';
import { buttonVariants } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Skeleton } from '@/components/ui/skeleton';
import usePropertyRentalRates from '@/hooks/usePropertyRentalRates';
import { cn } from '@/lib/utils';
import { BookingAvailabilityData } from '@/types/booking';
import { AvailabilityCalendarData, RentalRatesCalendarData } from '@/types/properties';
import { formatBookingFeesData } from '@/utils/booking';
import { isDateRangeSelected } from '@/utils/calendar';
import { currencyFormatterRound, Nullable, parseDateString } from '@/utils/common';
import {
  checkIfDateCannotBeCheckedIn,
  checkIfDateDisabledDesktop,
  formatDateRangePickerRentalRates,
  getBlockedRangesForDate,
  getBlockedStartAndEndDates,
  getCheckinDayRelativeToADate,
  getStartAndEndForcedCheckinDay,
  isForcedCheckinDay,
} from '@/utils/date-range-picker';

import {
  addYears,
  differenceInCalendarDays,
  endOfMonth,
  format,
  isBefore,
  isSameDay,
  isValid,
  startOfDay,
  startOfMonth,
  subMonths,
} from 'date-fns';

import RDPCustomDate from './RDPCustomDate';

type Props = {
  onClear: () => void;
  onClose: () => void;
  rentalRates?: RentalRatesCalendarData[];
  availableCalendar?: AvailabilityCalendarData[];
  propertyId: number;
  date?: DateRange;
  setDate: (_d?: DateRange) => void;
  bookingAvailabilityData?: Nullable<BookingAvailabilityData>;
  isFetchingBookingDetails?: boolean;
  showSave?: boolean;
  onSave?: () => void;
};

const CheckoutDateRangePicker = ({
  onClear,
  onClose,
  availableCalendar = [],
  rentalRates: rentalRatesDefault = [],
  propertyId,
  date,
  setDate,
  bookingAvailabilityData,
  isFetchingBookingDetails,
  showSave,
  onSave,
}: Props) => {
  // Calculate number of nights and booking data
  const numberOfNights = useMemo(
    () => (date?.from && date?.to ? differenceInCalendarDays(date?.to, date?.from) : 0),
    [date?.from, date?.to],
  );

  const bookingData = useMemo(
    () =>
      bookingAvailabilityData && formatBookingFeesData(bookingAvailabilityData, '', numberOfNights),
    [bookingAvailabilityData, numberOfNights],
  );

  // Date range configuration
  const maxSelectableDate = useMemo(() => endOfMonth(subMonths(addYears(new Date(), 2), 1)), []);

  // Fetch rental rates data
  const { data } = usePropertyRentalRates(
    propertyId,
    format(startOfMonth(new Date()), 'yyyy-MM-dd'),
    format(maxSelectableDate, 'yyyy-MM-dd'),
  );

  // Format rental rates data
  const rentalRatesMap = useMemo(() => {
    const rentalRatesSource = data.length > 0 ? data : rentalRatesDefault;
    return formatDateRangePickerRentalRates(rentalRatesSource);
  }, [data, rentalRatesDefault]);

  // Popup info state
  const [popUpInfo, setPopUpInfo] = useState<
    Nullable<{
      dt: Date;
      text: string;
    }>
  >(null);

  // Clear dates handler
  const onClearDates = useCallback(() => {
    onClear();
    setPopUpInfo(null);
  }, [onClear]);

  // Get blocked dates
  const { start: blockedStartDates, end: blockedEndDates } = useMemo(
    () =>
      getBlockedStartAndEndDates(
        availableCalendar.filter(
          (_a) =>
            !isBefore(
              startOfDay(parseDateString(_a.blockedFrom)),
              startOfDay(subMonths(new Date(), 1)),
            ) &&
            !isBefore(
              startOfDay(parseDateString(_a.blockedTo)),
              startOfDay(subMonths(new Date(), 1)),
            ),
        ),
      ),
    [availableCalendar],
  );

  // Date selection status
  const isCheckinSelected = useMemo(() => date?.from && isValid(date.from), [date?.from]);
  const isSelectedValid = useMemo(() => isDateRangeSelected(date), [date]);

  // Get rental data for start date
  const rentalDataForStartDate = useMemo(
    () =>
      date?.from &&
      (rentalRatesMap[format(date?.from, 'MMM-yyyy')] ?? []).find(
        (_r) => date?.from && isSameDay(parseDateString(_r.rateStartFrom), date?.from),
      ),
    [date?.from, rentalRatesMap],
  );

  // Header text based on selection state
  const headerText = useMemo(() => {
    if (isCheckinSelected) {
      if (isSelectedValid && date?.from && date?.to) {
        return `${differenceInCalendarDays(date?.to, date?.from)} nights`;
      }
      return 'Select check-out date';
    } else {
      return 'Select check-in date';
    }
  }, [isCheckinSelected, isSelectedValid, date?.from, date?.to]);

  // Mouse over date handler
  const throttledOnMouseOverDate = useMemo(
    () =>
      throttle((_dt: Date) => {
        const rentalRatesForDt = (rentalRatesMap[format(_dt, 'MMM-yyyy')] ?? []).find((_r) =>
          isSameDay(parseDateString(_r.rateStartFrom), _dt),
        );

        if (rentalRatesForDt?.allowCheckin && !date?.from) {
          return;
        }

        const checkinDay = getCheckinDayRelativeToADate(
          rentalRatesMap[format(_dt, 'MMM-yyyy')] ?? [],
          _dt,
          availableCalendar,
        );

        if (checkinDay) {
          setPopUpInfo({ dt: _dt, text: `From ${checkinDay} to ${checkinDay}` });
          return;
        }

        const isCheckInRestricted = checkIfDateCannotBeCheckedIn(
          _dt,
          checkinDay,
          rentalRatesForDt,
          rentalDataForStartDate,
        );

        if (
          (date?.from && !date?.to && isCheckInRestricted) ||
          (date?.from && isSameDay(date.from, _dt))
        ) {
          setPopUpInfo({
            dt: _dt,
            text: `${rentalDataForStartDate?.minimumNightsStay}-night minimum`,
          });
        }
      }, 100),
    [availableCalendar, date?.from, date?.to, rentalDataForStartDate, rentalRatesMap],
  );

  // Mouse leave date handler
  const throttledOnMouseLeaveDate = useMemo(() => throttle(() => setPopUpInfo(null), 100), []);

  // Check if date is selectable
  const isDateSelectable = useCallback(
    (dt: Date) => {
      const monthRates = rentalRatesMap[format(dt, 'MMM-yyyy')] ?? [];
      const rentalRate = monthRates.find((_r) => isSameDay(parseDateString(_r.rateStartFrom), dt));

      if (!rentalRate) return false;

      const checkinDay = getCheckinDayRelativeToADate(monthRates, dt, availableCalendar);
      const isCheckInRestricted = checkIfDateCannotBeCheckedIn(
        dt,
        checkinDay,
        rentalRate,
        rentalDataForStartDate,
      );

      if (
        (!date?.from && !rentalRate.allowCheckin) ||
        (date?.from && !rentalRate.allowCheckout) ||
        isCheckInRestricted
      ) {
        return false;
      }

      return true;
    },
    [availableCalendar, date?.from, rentalDataForStartDate, rentalRatesMap],
  );

  // Date selection handler
  const handleDateSelect = useCallback(
    (range: DateRange, dt: Date) => {
      if (!isDateSelectable(dt)) return;
      setDate(range);
    },
    [isDateSelectable, setDate],
  );

  // Calendar disabled dates
  const calendarDisabledDates = useMemo(
    () => [
      { before: new Date(), after: maxSelectableDate },
      (dt: Date) => {
        const blockedRanges = getBlockedRangesForDate(availableCalendar, dt);
        const checkinDay = getCheckinDayRelativeToADate(
          rentalRatesMap[format(dt, 'MMM-yyyy')] ?? [],
          dt,
          availableCalendar,
        );
        const rentalRate = (rentalRatesMap[format(dt, 'MMM-yyyy')] ?? []).find((_r) =>
          isSameDay(parseDateString(_r.rateStartFrom), dt),
        );
        const [firstCheckinDay, lastCheckinDay] = getStartAndEndForcedCheckinDay(
          rentalRatesMap[format(dt, 'MMM-yyyy')] ?? [],
        );

        const isBlockedStartDate = blockedStartDates.some((_d) =>
          isSameDay(startOfDay(dt), startOfDay(_d)),
        );
        const isBlockedEndDate = blockedEndDates.some((_d) =>
          isSameDay(startOfDay(dt), startOfDay(_d)),
        );
        const isDisabledDsktop = checkIfDateDisabledDesktop(
          checkinDay,
          firstCheckinDay,
          lastCheckinDay,
          date,
          rentalRate,
          availableCalendar,
        );

        const isDisabled =
          isDisabledDsktop ||
          (blockedRanges.length > 0 &&
            !date?.from &&
            !(isBlockedStartDate && !isBlockedEndDate) &&
            !(isBlockedEndDate && !isBlockedStartDate));

        return isDisabled;
      },
    ],
    [
      availableCalendar,
      blockedStartDates,
      blockedEndDates,
      date,
      rentalRatesMap,
      maxSelectableDate,
    ],
  );

  // Render day content
  const renderDayContent = useCallback(
    (props: DayContentProps) => (
      <RDPCustomDate
        {...props}
        selected={date}
        rentalRateForDate={(rentalRatesMap[format(props.date, 'MMM-yyyy')] ?? []).find((_r) =>
          isSameDay(parseDateString(_r.rateStartFrom), props.date),
        )}
        blockedStartDates={blockedStartDates}
        blockedEndDates={blockedEndDates}
        popUpInfo={popUpInfo}
        checkinDay={getCheckinDayRelativeToADate(
          rentalRatesMap[format(props.date, 'MMM-yyyy')] ?? [],
          props.date,
          availableCalendar,
        )}
        isForcedCheckinDay={isForcedCheckinDay(
          props.date,
          rentalRatesMap[format(props.date, 'MMM-yyyy')] ?? [],
        )}
      />
    ),
    [availableCalendar, blockedEndDates, blockedStartDates, date, popUpInfo, rentalRatesMap],
  );

  // Cleanup throttled functions
  useEffect(() => {
    return () => {
      throttledOnMouseOverDate.cancel();
      throttledOnMouseLeaveDate.cancel();
    };
  }, [throttledOnMouseLeaveDate, throttledOnMouseOverDate]);

  return (
    <>
      <div className="flex items-center justify-between pb-4">
        <div className="w-1/2 pl-4">
          <p className="m-0 text-xl font-medium">{headerText}</p>
          <p className="text-xs text-[#6D7380] m-0 mt-2">
            {isSelectedValid && date?.from && date?.to
              ? `${format(date?.from, 'LLL d, yyyy')} - ${format(date?.to, 'LLL d, yyyy')}`
              : `Add your travel dates for exact pricing`}
          </p>
        </div>
      </div>

      {/* Calendar */}
      <Calendar
        initialFocus
        fromMonth={new Date()}
        toMonth={maxSelectableDate}
        onAdditionalDayMouseEnter={throttledOnMouseOverDate}
        onDayMouseLeave={throttledOnMouseLeaveDate}
        components={{ DayContent: renderDayContent }}
        classNames={{
          day_range_start: 'day-range-start !text-white !bg-primary-blue !border-transparent ',
          day_range_end: 'day-range-end !text-white !bg-primary-blue !border-transparent',
          day: cn(
            buttonVariants({ variant: 'ghost' }),
            'relative cursor-pointer h-10 w-10 p-0 aria-selected:opacity-100 rounded-full border border-transparent border-solid hover:border-[#6D7380] hover:rounded-full [&:has(>.no-checkin)]:cursor-default [&:has(>.blockedBoundary)]:text-[#757575]',
          ),
        }}
        disabled={calendarDisabledDates}
        mode="range"
        defaultMonth={date?.from}
        selected={date}
        onSelect={handleDateSelect as SelectRangeEventHandler}
        numberOfMonths={2}
        className="p-0"
      />
      <div className="flex items-center justify-between gap-x-4 mt-2 h-[50px]">
        <div>
          {bookingAvailabilityData && isDateRangeSelected(date) ? (
            <>
              <span className="font-medium text-[24px]">
                {currencyFormatterRound.format(bookingData?.averageNightlyRate ?? 0)}
              </span>{' '}
              <span className="text-xs">Per Night</span>
            </>
          ) : isFetchingBookingDetails ? (
            <>
              <Skeleton className="w-[120px] h-6 mb-2.5" />
            </>
          ) : (
            <p className="m-0">Add date for price</p>
          )}
        </div>
        <div>
          <Button
            intent="ghost"
            className="border-solid border-carolina-blue-40 text-sm font-normal underline mr-4"
            onClick={onClearDates}
          >
            Clear dates
          </Button>
          {showSave ? (
            <Button
              className="font-medium rounded-[32px] w-[120px]"
              disabled={!isSelectedValid}
              onClick={onSave}
            >
              Save
            </Button>
          ) : (
            <Button
              intent="outline"
              className="text-sm font-normal rounded-[32px]"
              onClick={onClose}
            >
              Close
            </Button>
          )}
        </div>
      </div>
    </>
  );
};

export default CheckoutDateRangePicker;
