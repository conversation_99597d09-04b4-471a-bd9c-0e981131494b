'use client';

import { createContext, useContext, useEffect, useRef, useState } from 'react';

import { DateRange } from 'react-day-picker';
import { toast } from 'react-hot-toast';

import { GuestsValues } from '@/clients/components/common/GuestSelector';
import { checkBookingAvailability } from '@/services/server/booking';
import { BookingAvailabilityData } from '@/types/booking';
import { Nullable } from '@/types/common';
import { PetType, PropertyDetails } from '@/types/properties';
import { parseDateString } from '@/utils/common';
import {
  EnhacedEcomPropertyItem,
  formatSeoProperty,
  pushEcommerceDataLayer,
} from '@/utils/enhancedEcomAnanalytics';

import { format } from 'date-fns';

type RequestToBookContextType = {
  date?: DateRange;
  setDate: (date?: DateRange) => void;
  guests: GuestsValues;
  setGuests: (guests: GuestsValues) => void;
  petCount: number;
  setPetCount: (count: number) => void;
  petType: PetType;
  setPetType: (type: PetType) => void;
  isPetSelected: boolean;
  setIsPetSelected: (selected: boolean) => void;
  petDescription: string;
  setPetDescription: (desc: string) => void;
  bookingAvailabilityData: Nullable<BookingAvailabilityData>;
  setBookingAvailabilityData: (data: Nullable<BookingAvailabilityData>) => void;
  datePickerError: Nullable<string>;
  setDatePickerError: (err: Nullable<string>) => void;
  isFetchingBookingDetails: boolean;
  setIsFetchingBookingDetails: (status: boolean) => void;
};

const RequestToBookContext = createContext<RequestToBookContextType | undefined>(undefined);

type SearchParamsType = {
  adults?: string;
  children?: string;
  from?: string;
  to?: string;
  petCount?: string;
  petType?: string;
  petDescription?: string;
};

type RequestToBookContextProviderProps = {
  children: React.ReactNode;
  nrPropertyId: number;
  property: PropertyDetails;
  searchParams?: SearchParamsType;
};

export const RequestToBookContextProvider = ({
  children,
  nrPropertyId,
  property,
  searchParams,
}: RequestToBookContextProviderProps) => {
  // Initialize all state with default values
  const [date, setDate] = useState<DateRange | undefined>(undefined);
  const [guests, setGuests] = useState<GuestsValues>({ adults: 1, children: 0 });
  const [petCount, setPetCount] = useState<number>(1);
  const [petType, setPetType] = useState<PetType>(PetType.DOG);
  const [isPetSelected, setIsPetSelected] = useState<boolean>(false);
  const [petDescription, setPetDescription] = useState<string>('');
  const [bookingAvailabilityData, setBookingAvailabilityData] = useState<
    Nullable<BookingAvailabilityData>
  >(null);
  const [datePickerError, setDatePickerError] = useState<Nullable<string>>(null);
  const [isFetchingBookingDetails, setIsFetchingBookingDetails] = useState<boolean>(false);
  const [isClientSide, setIsClientSide] = useState<boolean>(false);

  const dateRef = useRef(date);

  // Set isClientSide to true after component mounts
  useEffect(() => {
    setIsClientSide(true);
  }, []);

  // Initialize all state values from searchParams after component mounts
  useEffect(() => {
    if (isClientSide && searchParams) {
      // Set guests
      setGuests({
        adults: searchParams.adults ? Number(searchParams.adults) : 1,
        children: searchParams.children ? Number(searchParams.children) : 0,
      });

      // Set pet-related state
      setPetCount(searchParams.petCount ? Number(searchParams.petCount) : 1);
      setPetType(searchParams.petType ? (searchParams.petType as PetType) : PetType.DOG);
      setIsPetSelected(!!(searchParams.petCount && Number(searchParams.petCount) > 0));
      setPetDescription(searchParams.petDescription ?? '');

      // Set date range
      if (searchParams.from && searchParams.to) {
        setDate({
          from: parseDateString(searchParams.from),
          to: parseDateString(searchParams.to),
        });
      }
    }
  }, [isClientSide, searchParams]);

  useEffect(() => {
    if (!date?.from || !date?.to) {
      setBookingAvailabilityData(null);
      setIsFetchingBookingDetails(false);
      return;
    }

    dateRef.current = date;

    const NOT_AVAILABLE_MSG = 'Not available for given dates.';

    const fetchBookingAvailability = async (from: Date, to: Date) => {
      setDatePickerError(null);
      setIsFetchingBookingDetails(true);

      try {
        const _data = await checkBookingAvailability<BookingAvailabilityData>(
          nrPropertyId,
          1,
          0,
          format(from, 'yyyy-MM-dd'),
          format(to, 'yyyy-MM-dd'),
        );

        if (!_data.rent || !_data.total) {
          const errorMsg = (_data as any)?.details ?? NOT_AVAILABLE_MSG;
          toast.error(errorMsg);
          setDatePickerError(errorMsg);
          setBookingAvailabilityData(null);
          setIsFetchingBookingDetails(false);
          return;
        }

        if (dateRef.current?.from && dateRef.current?.to) {
          setBookingAvailabilityData(_data);
          setIsFetchingBookingDetails(false);
        }
      } catch (error) {
        console.error(error);
        setBookingAvailabilityData(null);
        setIsFetchingBookingDetails(false);
      }
    };

    const handler = setTimeout(() => {
      if (date?.from && date?.to) {
        fetchBookingAvailability(date.from, date.to);
      }
    }, 500); // debounce

    return () => clearTimeout(handler);
  }, [date, nrPropertyId]);

  useEffect(() => {
    const propertyEcomAnalyticsData = [
      formatSeoProperty((property as unknown) as EnhacedEcomPropertyItem),
    ];

    pushEcommerceDataLayer('view_cart', propertyEcomAnalyticsData);
  }, [property]);

  return (
    <RequestToBookContext.Provider
      value={{
        date,
        setDate,
        guests,
        setGuests,
        petCount,
        setPetCount,
        petType,
        setPetType,
        isPetSelected,
        setIsPetSelected,
        petDescription,
        setPetDescription,
        bookingAvailabilityData,
        setBookingAvailabilityData,
        datePickerError,
        setDatePickerError,
        isFetchingBookingDetails,
        setIsFetchingBookingDetails,
      }}
    >
      {children}
    </RequestToBookContext.Provider>
  );
};

export const useRequestToBook = () => {
  const context = useContext(RequestToBookContext);
  if (!context) {
    throw new Error('useRequestToBook must be used within a RequestToBookContextProvider');
  }
  return context;
};
