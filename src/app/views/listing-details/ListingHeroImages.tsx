import ViewAllPhotos from '@/clients/views/listing-details/ViewAllPhotos';
import { PropertyPic } from '@/types/properties';

import Image from 'next/image';

type Props = {
  nrPropertyPics: PropertyPic[];
  nrPropertyId: number;
};

const ListingHeroImages = ({ nrPropertyPics, nrPropertyId }: Props) => {
  return (
    <>
      <div className="flex md:px-5 md:pb-5 gap-x-1 relative">
        <Image
          alt="Property Hero image"
          src={nrPropertyPics?.[0]?.nrPropertyPicPath ?? 'https://fakeimg.pl/300/'}
          width={0}
          height={0}
          priority
          sizes="100vw, 60vw"
          className="w-full lg:w-[70%] xl:w-[60%] h-[320px] md:h-[650px] md:rounded-[12px] object-cover"
        />
        <div className="hidden md:flex flex-col xl:flex-row gap-1 flex-wrap">
          <Image
            alt="Property image"
            src={nrPropertyPics?.[1]?.nrPropertyPicPath ?? 'https://fakeimg.pl/300/'}
            width={0}
            height={0}
            loading="lazy"
            sizes="25vw"
            className="hidden lg:block lg:w-full xl:w-[calc(50%-2px)] h-[325px] rounded-[12px] object-cover"
          />
          <Image
            alt="Property image"
            src={nrPropertyPics?.[2]?.nrPropertyPicPath ?? 'https://fakeimg.pl/300/'}
            width={0}
            height={0}
            loading="lazy"
            sizes="25vw"
            className="hidden lg:block lg:w-full xl:w-[calc(50%-2px)] h-[325px] rounded-[12px] object-cover"
          />
          <Image
            alt="Property image"
            src={nrPropertyPics?.[3]?.nrPropertyPicPath ?? 'https://fakeimg.pl/300/'}
            width={0}
            height={0}
            loading="lazy"
            sizes="25vw"
            className="hidden xl:block lg:w-full xl:w-[calc(50%-2px)] h-[325px] rounded-[12px] object-cover"
          />
          <Image
            alt="Property image"
            src={nrPropertyPics?.[4]?.nrPropertyPicPath ?? 'https://fakeimg.pl/300/'}
            width={0}
            height={0}
            loading="lazy"
            sizes="25vw"
            className="hidden xl:block lg:w-full xl:w-[calc(50%-2px)] h-[325px] rounded-[12px] object-cover"
          />
        </div>
        <ViewAllPhotos
          nrPropertyId={nrPropertyId}
          defaultData={nrPropertyPics}
          className="absolute right-5 lg:right-10 bottom-5 lg:bottom-10 backdrop-blur-[6px] rounded-xl border-0 overflow-hidden"
        >
          <div className="bg-black/20 flex items-center gap-x-4 text-white px-2 lg:px-6 py-2 lg:py-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="50"
              height="50"
              viewBox="0 0 50 50"
              fill="none"
              className="hidden lg:block"
            >
              <path
                d="M37.5 16.6665C37.5 18.9677 35.6345 20.8332 33.3333 20.8332C31.0322 20.8332 29.1667 18.9677 29.1667 16.6665C29.1667 14.3653 31.0322 12.4998 33.3333 12.4998C35.6345 12.4998 37.5 14.3653 37.5 16.6665Z"
                fill="white"
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M24.8805 2.604H25.1196C29.9287 2.60398 33.6975 2.60396 36.6381 2.99931C39.648 3.40398 42.023 4.24844 43.8872 6.11262C45.7514 7.97681 46.5959 10.3519 47.0005 13.3617C47.3959 16.3023 47.3959 20.0712 47.3958 24.8803V25.0642C47.3959 29.0409 47.3959 32.2963 47.1799 34.9468C46.9629 37.6104 46.5181 39.8361 45.5227 41.6847C45.0836 42.5 44.5441 43.2302 43.8872 43.8871C42.023 45.7512 39.648 46.5957 36.6381 47.0004C33.6975 47.3957 29.9287 47.3957 25.1195 47.3957H24.8805C20.0713 47.3957 16.3025 47.3957 13.3619 47.0004C10.352 46.5957 7.97699 45.7512 6.11281 43.8871C4.46014 42.2344 3.60668 40.1774 3.15284 37.6257C2.707 35.1191 2.62544 32.0005 2.6085 28.1279C2.60419 27.1429 2.60419 26.101 2.60419 25.0019V24.8803C2.60416 20.0712 2.60414 16.3023 2.99949 13.3617C3.40416 10.3519 4.24862 7.97681 6.11281 6.11262C7.97699 4.24844 10.352 3.40398 13.3619 2.99931C16.3025 2.60396 20.0713 2.60398 24.8805 2.604ZM13.7783 6.09644C11.115 6.45452 9.5093 7.13555 8.32251 8.32233C7.13573 9.50911 6.4547 11.1148 6.09663 13.7781C5.73251 16.4864 5.72919 20.0452 5.72919 24.9998C5.72919 25.6051 5.72919 26.1904 5.7299 26.7571L7.8156 24.9321C9.71408 23.271 12.5754 23.3663 14.3591 25.15L23.2961 34.087C24.7278 35.5187 26.9815 35.7139 28.6381 34.5497L29.2594 34.1131C31.6432 32.4378 34.8684 32.6319 37.0341 34.581L42.9308 39.8881C43.5244 38.6415 43.8769 37.0036 44.0652 34.693C44.2697 32.1837 44.2708 29.0537 44.2708 24.9998C44.2708 20.0452 44.2675 16.4864 43.9034 13.7781C43.5453 11.1148 42.8643 9.50911 41.6775 8.32233C40.4907 7.13555 38.8851 6.45452 36.2217 6.09644C33.5134 5.73232 29.9547 5.72901 25 5.72901C20.0454 5.72901 16.4866 5.73232 13.7783 6.09644Z"
                fill="white"
              />
            </svg>
            <span className="text-sm lg:text-lg">All Photos</span>
          </div>
        </ViewAllPhotos>
      </div>
    </>
  );
};

export default ListingHeroImages;
