import LoginButton from '@/clients/components/common/LoginButton';
import UserMenuComponent from '@/clients/components/common/UserMenuComponent';
import { getUserProfile } from '@/services/server/profile';
import { ProfileData, USERTYPE_PAYLOAD } from '@/types/profile';
import { mapUserTypeFromPayload } from '@/utils/users';

const UserMenuWrapper = async () => {
  const [{ data: userData, usertype }] = await Promise.all([
    getUserProfile<{ data: ProfileData; usertype: USERTYPE_PAYLOAD }>(),
  ]);

  return (
    <UserMenuComponent userData={userData} userType={mapUserTypeFromPayload(usertype)}>
      <div className="hidden lg:block">
        {userData ? (
          <div className="bg-primary-blue border-solid rounded-full text-white aspect-square w-10 h-10 flex items-center justify-center cursor-pointer">
            {userData?.firstname?.split('')?.[0]}
          </div>
        ) : (
          <LoginButton className="rounded-[30px] cursor-pointer font-medium px-8 h-[48px] border border-white border-solid" />
        )}
      </div>
    </UserMenuComponent>
  );
};

export default UserMenuWrapper;
